#!/usr/bin/env ts-node

/**
 * Test script for Messenger Report S3 integration
 * 
 * This script tests the messenger report generation and S3 integration.
 * Run with: npx ts-node src/scripts/test-messenger-report.ts
 */

import axios from 'axios';
import { logger } from '../config/logger';

async function testMessengerReport(): Promise<void> {
  console.log('🚀 Testing Messenger Report Generation\n');
  
  try {
    const baseUrl = process.env.API_BASE_URL || 'http://localhost:5000';
    const endpoint = `${baseUrl}/api/report/v1.0/messenger-image`;
    
    console.log('📱 Generating messenger report...');
    console.log(`   Endpoint: ${endpoint}`);
    
    // Test with current date
    const testPayload = {
      text: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
      // drId: 'optional-doctor-id', // Uncomment to test doctor-specific reports
    };
    
    console.log(`   Payload: ${JSON.stringify(testPayload, null, 2)}`);
    
    const response = await axios.post(endpoint, testPayload, {
      timeout: 30000, // 30 second timeout
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (response.status === 200) {
      console.log('✅ Messenger report generated successfully!');
      console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      
      if (response.data.s3Key) {
        console.log(`   S3 Key: ${response.data.s3Key}`);
      }
      
      if (response.data.fileUrl) {
        console.log(`   File URL: ${response.data.fileUrl}`);
      }
      
      if (response.data.reportData) {
        console.log('\n📊 Report Data:');
        const data = response.data.reportData;
        console.log(`   Report Date: ${data.reportDate}`);
        console.log(`   Doctor: ${data.doctorName || 'All Doctors'}`);
        console.log(`   Pending Review: ${data.pendingReview}`);
        console.log(`   Approved (24h): ${data.approved}`);
        console.log(`   Rejected (24h): ${data.rejected}`);
        console.log(`   Total Requests: ${data.total}`);
      }
      
      console.log('\n🎉 Test completed successfully!');
      console.log('📱 Check Slack for the messenger report image.');
      
    } else {
      throw new Error(`Unexpected response status: ${response.status}`);
    }
    
  } catch (error) {
    console.error('❌ Messenger Report Test Failed:');
    
    if (axios.isAxiosError(error)) {
      console.error(`   HTTP Status: ${error.response?.status}`);
      console.error(`   Error Message: ${error.response?.data?.message || error.message}`);
      console.error(`   Response Data: ${JSON.stringify(error.response?.data, null, 2)}`);
    } else {
      console.error(`   Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    console.log('\n🔧 Troubleshooting Tips:');
    console.log('   1. Ensure the API server is running');
    console.log('   2. Check database connection and migrations');
    console.log('   3. Verify AWS S3 credentials and bucket access');
    console.log('   4. Check Slack token and channel configuration');
    console.log('   5. Review application logs for detailed errors');
    
    process.exit(1);
  }
}

// Test different scenarios
async function runAllTests(): Promise<void> {
  console.log('🧪 Running Messenger Report Tests\n');
  
  try {
    // Test 1: Current date report
    console.log('📅 Test 1: Current Date Report');
    await testMessengerReport();
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Test 2: Specific date report
    console.log('📅 Test 2: Specific Date Report (Yesterday)');
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];
    
    const baseUrl = process.env.API_BASE_URL || 'http://localhost:5000';
    const endpoint = `${baseUrl}/api/report/v1.0/messenger-image`;
    
    const response = await axios.post(endpoint, {
      text: yesterdayStr,
    });
    
    console.log(`✅ Yesterday's report generated: ${response.data.success}`);
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  const testType = process.argv[2];
  
  if (testType === 'all') {
    runAllTests().catch(error => {
      logger.error('Test suite execution failed:', error);
      console.error('💥 Test suite execution failed:', error.message);
      process.exit(1);
    });
  } else {
    testMessengerReport().catch(error => {
      logger.error('Test execution failed:', error);
      console.error('💥 Test execution failed:', error.message);
      process.exit(1);
    });
  }
}

export { testMessengerReport, runAllTests };
