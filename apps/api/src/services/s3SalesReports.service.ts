import { PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import bucketClient from '../config/bucketClient';
import config from '../config';
import { db } from '../utils/db';
import { logger } from '../config/logger';
import { DateTime } from 'luxon';

interface SalesReportFile {
  id?: string;
  reportDate: string;
  reportType: 'daily' | 'weekly' | 'monthly' | 'custom';
  reportFormat: 'png' | 'pdf' | 'html';
  s3Bucket: string;
  s3Key: string;
  fileUrl: string;
  fileName: string;
  fileSizeBytes?: number;
  contentType: string;
  reportTitle?: string;
  reportDescription?: string;
  reportData?: Record<string, unknown>;
  slackChannel?: string;
  slackMessageTs?: string;
  slackSentAt?: Date;
  status: 'generated' | 'uploaded' | 'sent' | 'failed' | 'archived';
  errorMessage?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

interface UploadResult {
  success: boolean;
  fileUrl?: string;
  s3Key?: string;
  error?: string;
}

export class S3SalesReportsService {
  private bucketName: string;

  constructor() {
    this.bucketName = config.awsBucketName;
  }

  /**
   * Upload a sales report file to S3 and store metadata in database
   */
  async uploadSalesReport(
    fileBuffer: Buffer,
    reportDate: string,
    reportType: 'daily' | 'weekly' | 'monthly' | 'custom',
    reportFormat: 'png' | 'pdf' | 'html' = 'png',
    options: {
      reportTitle?: string;
      reportDescription?: string;
      reportData?: Record<string, unknown>;
      slackChannel?: string;
    } = {}
  ): Promise<UploadResult> {
    const client = await db.connect();
    
    try {
      await client.query('BEGIN');

      // Generate S3 key with organized folder structure
      const s3Key = this.generateS3Key(reportDate, reportType, reportFormat);
      const fileName = this.extractFileNameFromKey(s3Key);
      const contentType = this.getContentType(reportFormat);

      // Upload to S3
      const uploadCommand = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
        Body: fileBuffer,
        ContentType: contentType,
        Metadata: {
          reportDate,
          reportType,
          uploadedAt: new Date().toISOString(),
        },
      });

      await bucketClient.send(uploadCommand);

      // Generate file URL
      const fileUrl = `https://${this.bucketName}.s3.${config.awsRegion}.amazonaws.com/${s3Key}`;

      // Store metadata in database
      const reportFile: SalesReportFile = {
        reportDate,
        reportType,
        reportFormat,
        s3Bucket: this.bucketName,
        s3Key,
        fileUrl,
        fileName,
        fileSizeBytes: fileBuffer.length,
        contentType,
        reportTitle: options.reportTitle,
        reportDescription: options.reportDescription,
        reportData: options.reportData,
        slackChannel: options.slackChannel,
        status: 'uploaded',
      };

      const insertQuery = `
        INSERT INTO sales_report_files (
          report_date, report_type, report_format, s3_bucket, s3_key, 
          file_url, file_name, file_size_bytes, content_type, 
          report_title, report_description, report_data, slack_channel, status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        ON CONFLICT (report_date, report_type, report_format) 
        DO UPDATE SET 
          s3_bucket = EXCLUDED.s3_bucket,
          s3_key = EXCLUDED.s3_key,
          file_url = EXCLUDED.file_url,
          file_name = EXCLUDED.file_name,
          file_size_bytes = EXCLUDED.file_size_bytes,
          content_type = EXCLUDED.content_type,
          report_title = EXCLUDED.report_title,
          report_description = EXCLUDED.report_description,
          report_data = EXCLUDED.report_data,
          slack_channel = EXCLUDED.slack_channel,
          status = EXCLUDED.status,
          updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `;

      const result = await client.query(insertQuery, [
        reportFile.reportDate,
        reportFile.reportType,
        reportFile.reportFormat,
        reportFile.s3Bucket,
        reportFile.s3Key,
        reportFile.fileUrl,
        reportFile.fileName,
        reportFile.fileSizeBytes,
        reportFile.contentType,
        reportFile.reportTitle,
        reportFile.reportDescription,
        reportFile.reportData ? JSON.stringify(reportFile.reportData) : null,
        reportFile.slackChannel,
        reportFile.status,
      ]);

      await client.query('COMMIT');

      logger.info('Sales report uploaded to S3 successfully', {
        reportDate,
        reportType,
        s3Key,
        fileUrl,
        fileSizeBytes: fileBuffer.length,
        recordId: result.rows[0]?.id,
      });

      return {
        success: true,
        fileUrl,
        s3Key,
      };
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to upload sales report to S3', {
        error,
        reportDate,
        reportType,
        bucketName: this.bucketName,
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    } finally {
      client.release();
    }
  }

  /**
   * Update Slack message information for a report
   */
  async updateSlackInfo(
    reportDate: string,
    reportType: string,
    slackMessageTs: string,
    slackChannel: string
  ): Promise<void> {
    const client = await db.connect();

    try {
      const updateQuery = `
        UPDATE sales_report_files 
        SET slack_message_ts = $1, slack_channel = $2, slack_sent_at = CURRENT_TIMESTAMP, 
            status = 'sent', updated_at = CURRENT_TIMESTAMP
        WHERE report_date = $3 AND report_type = $4
      `;

      await client.query(updateQuery, [slackMessageTs, slackChannel, reportDate, reportType]);

      logger.info('Updated Slack info for sales report', {
        reportDate,
        reportType,
        slackMessageTs,
        slackChannel,
      });
    } catch (error) {
      logger.error('Failed to update Slack info for sales report', {
        error,
        reportDate,
        reportType,
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Get sales report file information from database
   */
  async getSalesReport(reportDate: string, reportType: string): Promise<SalesReportFile | null> {
    const client = await db.connect();

    try {
      const query = `
        SELECT * FROM sales_report_files 
        WHERE report_date = $1 AND report_type = $2
        ORDER BY created_at DESC
        LIMIT 1
      `;

      const result = await client.query(query, [reportDate, reportType]);

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      return {
        id: row.id,
        reportDate: row.report_date,
        reportType: row.report_type,
        reportFormat: row.report_format,
        s3Bucket: row.s3_bucket,
        s3Key: row.s3_key,
        fileUrl: row.file_url,
        fileName: row.file_name,
        fileSizeBytes: row.file_size_bytes,
        contentType: row.content_type,
        reportTitle: row.report_title,
        reportDescription: row.report_description,
        reportData: row.report_data,
        slackChannel: row.slack_channel,
        slackMessageTs: row.slack_message_ts,
        slackSentAt: row.slack_sent_at,
        status: row.status,
        errorMessage: row.error_message,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      };
    } finally {
      client.release();
    }
  }

  /**
   * Generate S3 key with organized folder structure
   */
  private generateS3Key(reportDate: string, reportType: string, reportFormat: string): string {
    const date = DateTime.fromISO(reportDate);
    const year = date.year;
    const month = date.toFormat('MM');
    
    return `sales-reports/${reportType}/${year}/${month}/report-${reportDate}.${reportFormat}`;
  }

  /**
   * Extract filename from S3 key
   */
  private extractFileNameFromKey(s3Key: string): string {
    return s3Key.split('/').pop() || s3Key;
  }

  /**
   * Get content type based on file format
   */
  private getContentType(format: string): string {
    const contentTypes: Record<string, string> = {
      png: 'image/png',
      pdf: 'application/pdf',
      html: 'text/html',
    };
    return contentTypes[format] || 'application/octet-stream';
  }

  /**
   * Generate a presigned URL for temporary access to a file
   */
  async getPresignedUrl(s3Key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
      });

      const url = await getSignedUrl(bucketClient, command, { expiresIn });
      return url;
    } catch (error) {
      logger.error('Failed to generate presigned URL', { error, s3Key });
      throw error;
    }
  }

  /**
   * Delete a sales report file from S3 and database
   */
  async deleteSalesReport(reportDate: string, reportType: string): Promise<boolean> {
    const client = await db.connect();

    try {
      await client.query('BEGIN');

      // Get file info first
      const report = await this.getSalesReport(reportDate, reportType);
      if (!report) {
        logger.warn('Sales report not found for deletion', { reportDate, reportType });
        return false;
      }

      // Delete from S3
      const deleteCommand = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: report.s3Key,
      });

      await bucketClient.send(deleteCommand);

      // Delete from database
      const deleteQuery = `
        DELETE FROM sales_report_files 
        WHERE report_date = $1 AND report_type = $2
      `;

      await client.query(deleteQuery, [reportDate, reportType]);

      await client.query('COMMIT');

      logger.info('Sales report deleted successfully', {
        reportDate,
        reportType,
        s3Key: report.s3Key,
      });

      return true;
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to delete sales report', {
        error,
        reportDate,
        reportType,
      });
      return false;
    } finally {
      client.release();
    }
  }
}

export default S3SalesReportsService;
