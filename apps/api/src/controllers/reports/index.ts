import { RequestHandler } from 'express-serve-static-core';
import { catchAll } from '../../utils/catchAll';
import { WordPressUtils } from '../../helpers/wordpress';
import { DateTime } from 'luxon';
import { WebClient } from '@slack/web-api';
import config from '../../config';
import { logger } from '../../config/logger';
import axios, { AxiosError } from 'axios';
import { db } from '../../utils/db';
import {
  AGENTSLIST,
  csvToJson,
  CurrentBalance,
  findChurnedPatients,
  findStartAndEndDate,
  generateAgentReport,
  generateDoctorSupplyRequests,
  generateProductCentricSupply,
  Orders,
  QUERY_CONVERSION,
  ZohoConversion,
  ZohoRecord,
  AgentReport,
} from './helpers';
import { ZohoAuth, zohoQueryURL } from '../../helpers/zoho';
import httpStatus from 'http-status';
import { ApiError } from '../../utils/ApiError';
import { eventReportingService } from '../../services/eventReporting.service';
import puppeteer from 'puppeteer';
import S3SalesReportsService, { REPORT_TYPES } from '../../services/s3SalesReports.service';
// import { MOCK_DATA } from './mockData';

let jwtToken: string | undefined = undefined;
let tokenExpiresAt: number | undefined = undefined;

export const churnReport: RequestHandler = catchAll(async (req, res) => {
  const tps = await WordPressUtils.getTPs();
  const account = config.googleSheet;
  const googleSheetURL = `${account}${req.body.sheetURL}`;
  const startingDataPoint = req.body.startingDate
    ? DateTime.fromFormat(req.body.startingDate, 'yyyy-MM-dd')
    : DateTime.fromFormat('1970-01-01', 'yyyy-MM-dd');

  const current = req.body.text ? DateTime.fromFormat(req.body.text, 'yyyy-MM-dd') : DateTime.utc();
  const today = current.toFormat('yyyy-MM-dd');
  const targetDates = findStartAndEndDate(current);
  logger.info(`Sending report for ${today} ...`);

  let startingDateForOrders = DateTime.utc() as DateTime<true> | DateTime<false>;

  // filter TPs that fall between our target period.
  const targetTps = tps.data.filter((e) => {
    if (!e['Treatment Plans Start Date']) {
      return false;
    }

    if (!e['Treatment Plans End Date']) {
      return false;
    }

    const tpStartDate = DateTime.fromISO(e['Treatment Plans Start Date'], { zone: 'utc' });
    const tpEndDate = DateTime.fromISO(e['Treatment Plans End Date'], { zone: 'utc' });

    const start = DateTime.fromISO(targetDates.start, { zone: 'utc' });
    const end = DateTime.fromISO(targetDates.end, { zone: 'utc' });

    // Validate all dates
    if (!start.isValid || !end.isValid) {
      console.warn('Invalid date detected:', {
        firstDay: targetDates.start,
        lastDay: targetDates.end,
      });
      return false;
    }
    // return tpStartDate.startOf('day') >= start.startOf('day') && tpStartDate.startOf('day') <= end.startOf('day');
    if (
      tpEndDate.startOf('day') >= start.startOf('day') &&
      tpStartDate.startOf('day') <= end.startOf('day') &&
      tpStartDate.startOf('day') >= startingDataPoint.startOf('day')
    ) {
      startingDateForOrders =
        tpStartDate.startOf('day') < startingDateForOrders ? tpStartDate.startOf('day') : startingDateForOrders;
      return true;
    }
    return false;
  });

  const defaultStartOrders = startingDateForOrders.toFormat('yyyy-MM-dd');
  const orders = await WordPressUtils.getOrders(defaultStartOrders, today);

  const tpPatientEmails = new Set<string>(targetTps.map((tp): string => tp.user_email.toLowerCase().trim() as string));
  const churnedPatient: string[] = [];
  const committedPatient: string[] = [];

  const uniqueTpEmails: string[] = [...tpPatientEmails];
  const filteredOrders = orders.data.filter((order) => {
    const orderEmail = order.user_email?.toLowerCase().trim();
    return orderEmail && tpPatientEmails.has(orderEmail.toLowerCase().trim());
  });

  const orderPatientEmail = new Set(filteredOrders.map((order) => order.user_email.toLowerCase().trim()));

  const churnedPatients = findChurnedPatients(targetTps, filteredOrders);
  const expiredTP = churnedPatients.filter((p) => p.tpExpired === true);
  const highConfidenceChurned = churnedPatients.filter((p) => p.churnedConfidence === 'high');

  uniqueTpEmails.forEach((email) => {
    if (orderPatientEmail.has(email.toLowerCase().trim())) {
      committedPatient.push(email);
    } else {
      churnedPatient.push(email);
    }
  });

  const ratio = (((churnedPatients.length - expiredTP.length) / uniqueTpEmails.length) * 100).toFixed(2);
  const highConfidenceRatio = ((highConfidenceChurned.length / uniqueTpEmails.length) * 100).toFixed(2);

  const buildSlackReport = () => {
    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: `📊 CHURN Report for ${today}`,
          emoji: true,
        },
      },

      {
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `Find detailed reporting here ${googleSheetURL}`,
          },
        ],
      },

      {
        type: 'divider',
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*Key Metrics*',
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Report Date:*\n${today}`,
          },
          {
            type: 'mrkdwn',
            text: `*Active TPs Date Range:*\n${targetDates.start} to ${targetDates.end}`,
          },
          {
            type: 'mrkdwn',
            text: `*Treatment Plans:*\n${uniqueTpEmails.length}`,
          },
          {
            type: 'mrkdwn',
            text: `*Churn Count:*\n${churnedPatients.length}`,
          },
          {
            type: 'mrkdwn',
            text: `*Churn Rate:*\n${ratio}%`,
          },
          {
            type: 'mrkdwn',
            text: `*Total TPs Expired:*\n${expiredTP.length}`,
          },
        ],
      },
      {
        type: 'divider',
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*High confidence churned ratio:*\n${highConfidenceRatio}%`,
          },
        ],
      },
      {
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: 'Patient with last purchased older than 28 days',
          },
        ],
      },
      {
        type: 'divider',
      },
      {
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: '_Report period covers 35 days (28 days + 7 day grace period)_',
          },
        ],
      },
    ];
    return blocks;
  };

  const slack = new WebClient(config.slackToken); // Use environment variable
  const channel = config.googleWeeklyReportChannel;

  // 'C08FW6GA9U4' test
  // https://harvest-australia.slack.com/archives/C08FW6GA9U4
  await slack.chat.postMessage({
    channel: channel,
    blocks: buildSlackReport(),
    text: 'Church Report',
  });

  logger.info(`Sending report for ${today} ... DONE`);

  res.status(200).send({
    churnedPatients,
  });
});

async function getPatientStatusAsLeads(): Promise<ZohoRecord[]> {
  const client = await db.connect();
  try {
    const result = await client.query<ZohoRecord>(`

WITH patient_statuses AS (
    -- Away patients (yesterday only)
    SELECT
        p."zohoID" as zoho_id,
        p."fullName" as full_name,
        p."mobile" as phone_number,
        'Away' as use_case
    FROM patient p
    JOIN patientqueue pq ON p."patientID" = pq."patientID"
    WHERE pq.status = 'AWAY'
      AND pq."createdAt" >= CURRENT_DATE - INTERVAL '1 day'
      AND pq."createdAt" < CURRENT_DATE
    UNION ALL
    -- Drop-off patients (yesterday only)
    SELECT
        p."zohoID" as zoho_id,
        p."fullName" as full_name,
        p."mobile" as phone_number,
        'Drop_offs' as use_case
    FROM patient p
    JOIN patientqueue pq ON p."patientID" = pq."patientID"
    WHERE pq."joinedAt" IS NOT NULL
      AND pq."createdAt" >= CURRENT_DATE - INTERVAL '1 day'
      AND pq."createdAt" < CURRENT_DATE
      AND pq."completedAt" IS NULL
),
latest_consultations AS (
    SELECT DISTINCT ON (c."patientID")
        p."zohoID" as zoho_id,
        p."fullName" as full_name,
        p."mobile" as phone_number
    FROM consultation c
    JOIN patient p ON c."patientID" = p."patientID"
    WHERE c."queueTag" = 'no-show'
      AND c."createdAt" >= CURRENT_DATE - INTERVAL '1 day'
      AND c."createdAt" < CURRENT_DATE
    ORDER BY c."patientID", c."createdAt" DESC
)
SELECT
    zoho_id as "zohoID",
    'Leads' as "module",
    'Notified_Noshow' as "useCase",
    phone_number as "phoneNumber",
    full_name as "fullName"
FROM latest_consultations
UNION ALL
SELECT
    zoho_id as "zohoID",
    'Leads' as "module",
    use_case as "useCase",
    phone_number as "phoneNumber",
    full_name as "fullName"
FROM patient_statuses;
 `);
    return result.rows;
  } catch (error) {
    logger.error('Error fetching patient status as leads:', error);
    throw new Error('Failed to retrieve patient status data');
  } finally {
    client.release();
  }
}

export const getLeads: RequestHandler = catchAll(async (_req, res) => {
  try {
    // 1. Get patient status data
    const records = await getPatientStatusAsLeads();

    if (records.length === 0) {
      res.status(200).json({
        success: true,
        message: 'No patient status records found to process',
        count: 0,
      });
      return;
    }

    const headers = {
      'Content-Type': 'application/json',
    };

    // Corrected API call with proper endpoint concatenation
    const response = await axios.post(`${config.autodialerApiUrl}/post-Patients`, records, { headers });

    // 3. Post to API (production or local mock)
    // 4. Return success response
    res.status(200).json({
      success: true,
      message: 'Successfully sent patient status to autodialer',
      count: records.length,
      responseData: response.data,
    });
    return;
  } catch (error) {
    console.error('Error in postPatientStatusAsLeads:', error);

    const errorMessage = axios.isAxiosError(error)
      ? error.response?.data?.message || error.message
      : 'Failed to process patient status leads';

    res.status(500).json({
      success: false,
      message: errorMessage,
    });
    return;
  }
});

export const getNewLeads: RequestHandler = catchAll(async (_req, res) => {
  try {
    // 1. Get patient status data
    const records = await getPatientStatusAsLeads();

    if (records.length === 0) {
      res.status(200).json({
        success: true,
        message: 'No patient status records found to process',
        count: 0,
      });
      return;
    }

    const headers = {
      'Content-Type': 'application/json',
    };

    // Corrected API call with proper endpoint concatenation
    const response = await axios.post(`${config.autodialerApiUrl}/post-Patients`, records, { headers });

    // 3. Post to API (production or local mock)
    // 4. Return success response
    res.status(200).json({
      success: true,
      message: 'Successfully sent patient status to autodialer',
      count: records.length,
      responseData: response.data,
    });
    return;
  } catch (error) {
    console.error('Error in postPatientStatusAsLeads:', error);

    const errorMessage = axios.isAxiosError(error)
      ? error.response?.data?.message || error.message
      : 'Failed to process patient status leads';

    res.status(500).json({
      success: false,
      message: errorMessage,
    });
    return;
  }
});

async function getAuthToken(): Promise<string> {
  const isTokenValid = jwtToken && tokenExpiresAt && Date.now() < tokenExpiresAt;

  if (isTokenValid) {
    return jwtToken!;
  }

  try {
    const response = await axios.post(`${config.softphoneProURL}/login/api/auth`, {
      login: config.softphoneProEmail,
      password: config.softphoneProPassword,
    });

    const cookie = response.headers['set-cookie']?.[0];
    jwtToken = cookie?.split(';')[0].split('=')[1];

    const expiresPart = cookie?.split(';').find((part) => part.trim().startsWith('Expires='));

    if (expiresPart) {
      const expiresDate = new Date(expiresPart.trim().split('=')[1]);
      tokenExpiresAt = expiresDate.getTime();
    } else {
      tokenExpiresAt = Date.now() + 60 * 60 * 1000; // default to 1 hour if missing
    }

    return jwtToken!;
  } catch (error) {
    logger.error('Error fetching auth token:', error);
    throw new Error('Failed to retrieve authentication token');
  }
}

export const buildSalesAgentReport: RequestHandler = catchAll(async (_req, res) => {
  try {
    const token = await getAuthToken();
    const current = DateTime.utc();
    const nextDay = current.plus({ days: 1 });

    const from = current.toFormat('yyyy-MM-dd');
    const to = nextDay.toFormat('yyyy-MM-dd');

    const queryParams = `https://vas.softphone.pro/api/history/calls/export?dateFrom=${from}T00:00:00%2B05:00&dateTo=${to}T00:00:00%2B05:00&localTimeZoneOffset=%2B300&typeFile=csv`;

    const getReportSoftphone = await axios.get(queryParams, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const csvData = getReportSoftphone.data;
    const data = csvToJson(csvData, ';');

    const headers = await ZohoAuth.getHeaders();

    const query = QUERY_CONVERSION();
    const response = await axios.post(zohoQueryURL, query, {
      headers,
    });
    const zohoData = response.data.data as ZohoConversion[];

    const report = generateAgentReport(data, zohoData, AGENTSLIST);

    const messages = {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '📊 Sales Team Daily Report',
            emoji: true,
          },
        },
        { type: 'divider' },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '*Agent Performance Summary*',
          },
        },
        ...report.flatMap((agent) => [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*Agent:* ${agent.agent}\n*Total Calls:* ${agent.calls}`,
            },
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Inbound:* ${agent.in}`,
              },
              {
                type: 'mrkdwn',
                text: `*Inbound Answered:* ${agent.ans} (${agent.percentAns})`,
              },
              {
                type: 'mrkdwn',
                text: `*Outbound:* ${agent.out}`,
              },
              {
                type: 'mrkdwn',
                text: `*Outbound Successful:* ${agent.suc} (${agent.percentSuc})`,
              },
              {
                type: 'mrkdwn',
                text: `*Bookings:* ${agent.bookings}`,
              },
              {
                type: 'mrkdwn',
                text: `*Total Talk Time:* ${agent.talkTime}`,
              },
              {
                type: 'mrkdwn',
                text: `*Total Wait Time:* ${agent.waitTime}`,
              },
            ],
          },
          { type: 'divider' },
        ]),
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: '*Legend:* Calls = Total, In = Inbound, Ans = Answered, Out = Outbound, Suc = Successful',
            },
          ],
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `:clock3: Report generated on ${new Date().toLocaleString()}`,
            },
          ],
        },
      ],
    };

    const slack = new WebClient(config.slackToken); // Use environment variable
    const channel = 'C08VC12V80N';

    // 'C08FW6GA9U4' test
    // https://harvest-australia.slack.com/archives/C08FW6GA9U4
    await slack.chat.postMessage({
      channel: channel,
      blocks: messages.blocks,
      text: 'Church Report',
    });

    logger.info(`Sales report for ${current.toFormat('yyyy-MM-dd')} sent successfully.`);

    // Store Data in our DB before sending to Slack
    res.status(200).json({
      success: true,
    });
    return;
  } catch (error) {
    logger.error('Error building sales report:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while building the sales report',
    });
    return;
  }
});

export const medecineRegister: RequestHandler = catchAll(async (req, res) => {
  const current = DateTime.utc();
  const nextDay = current.plus({ days: 1 });
  const client = await db.connect();
  const update = req.body.update;
  // Store the order number to avoid sending an order twice.
  const getBalanceQuery = `SELECT * FROM productstock`;
  const storeOldData = `INSERT INTO medregistermanagement (lastpull, output, productcentricdata, orders) VALUES(CURRENT_TIMESTAMP, $1, $2, $3)`;

  const updateBalanceQuery = (valuesClause: string) => `
    UPDATE productstock
    SET balance = balance - updates.total_bud_quantity
    FROM (VALUES ${valuesClause}) AS updates(code, total_bud_quantity)
    WHERE productstock.code = updates.code;
  `;

  try {
    const result = await client.query(`SELECT * FROM medregistermanagement ORDER BY lastpull DESC LIMIT 1`);
    const lastPullDate =
      result.rows.length > 0 ? DateTime.fromJSDate(new Date(result.rows?.[0].lastpull), { zone: 'utc' }) : undefined;
    const pullOldData = lastPullDate && lastPullDate.plus({ hours: 4 }) > current;

    if (pullOldData && lastPullDate && !update) {
      res.status(200).send({
        output: result.rows[0].output,
        productCentricData: result.rows[0].productcentricdata,
        orders: result.rows[0]?.orders,
        oldData: true,
        lastPullDate: lastPullDate.toFormat('yyyy-MM-dd HH:mm:ss'),
      });
      return;
    }
    const oldOrders = result.rows[0]?.orders || { data: [] };

    const orders = await WordPressUtils.getOrders('2025-06-08', nextDay.toFormat('yyyy-MM-dd'), 'paid');
    const filteredOrders = orders.data.filter((d) => !oldOrders.data.find((o) => o.order_number === d.order_number));
    const copyOfFilteredOrders = [...filteredOrders] as Orders[];
    // const orders = MOCK_DATA;
    const currentBalance = await client.query(getBalanceQuery);
    const output = generateDoctorSupplyRequests(orders.data);
    const productCentricData = generateProductCentricSupply(
      output,
      currentBalance.rows as CurrentBalance[],
      copyOfFilteredOrders,
    );

    if (copyOfFilteredOrders.length > 0) {
      const newOuput = generateDoctorSupplyRequests(copyOfFilteredOrders);
      const newProductCentricData = generateProductCentricSupply(
        newOuput,
        currentBalance.rows as CurrentBalance[],
        copyOfFilteredOrders,
      );
      const valuesClause = Object.entries(newProductCentricData)
        .map(([, data]) => `('${data.tradeName.replace(/'/g, "''")}', ${data.totalBudQuantity})`)
        .join(', ');
      await client.query(updateBalanceQuery(valuesClause));
    }

    await client.query(storeOldData, [output, productCentricData, orders]);

    res.status(200).send({
      output,
      productCentricData,
      orders,
      filteredOrders,
    });
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const verifyPin: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const pin = req.body.pin;
  const query = `SELECT * FROM medicineregisterpin WHERE pin = $1 AND active = true`;
  try {
    const result = await client.query(query, [pin]);
    if (result.rows.length <= 0) {
      res.status(200).send({
        found: false,
      });
      return;
    } else {
      res.status(200).send({
        found: true,
      });
      return;
    }
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

// Define the expected structure for an event
interface TrackedEvent {
  actor: string;
  target: string;
  action: string;
  message: string;
  timestamp: string;
  actor_id: string;
  target_id?: string;
}

export const trackEvent: RequestHandler = catchAll(async (req, res) => {
  const events: TrackedEvent[] = req.body.events;
  if (
    !Array.isArray(events) ||
    events.length === 0 ||
    !events.every(
      (e) =>
        typeof e.actor === 'string' &&
        typeof e.target === 'string' &&
        typeof e.action === 'string' &&
        typeof e.message === 'string' &&
        typeof e.timestamp === 'string',
    )
  ) {
    res.status(400).json({
      error: 'Invalid events data. Each event must have actor, target, action, message, and timestamp fields.',
    });
    return;
  }
  const client = await db.connect();
  const actors = events.map((e) => e.actor);
  const targets = events.map((e) => e.target);
  const actions = events.map((e) => e.action);
  const messages = events.map((e) => e.message);
  const timestamps = events.map((e) => e.timestamp);
  const actorIds = events.map((e) => e.actor_id);
  const targetIds = events.map((e) => e?.target_id);
  const eventsQuery = `
    INSERT INTO tracked_events (actor, target, action, message, timestamp, actor_id, target_id)
    SELECT * FROM UNNEST($1::text[], $2::text[], $3::text[], $4::text[], $5::text[], $6::text[], $7::text[])
    RETURNING id;
  `;
  try {
    await client.query('BEGIN');
    const result = await client.query(eventsQuery, [
      actors,
      targets,
      actions,
      messages,
      timestamps,
      actorIds,
      targetIds,
    ]);
    await client.query('COMMIT');
    res
      .status(200)
      .json({ success: true, message: 'Events tracked successfully', eventIds: result.rows.map((row) => row.id) });
    return;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Error tracking events:', error, { events });
    res.status(500).json({ error: 'Failed to track events' });
    return;
  } finally {
    client.release();
  }
});

export const sendDetailedReportToSlack: RequestHandler = catchAll(async (req, res) => {
  const doctor_access_id = req.body.doctor_access_id;
  const getDoctorIdQuery = `SELECT id FROM Dr WHERE "accessID" = $1::text`;
  const doctorIdResult = await db.query(getDoctorIdQuery, [doctor_access_id]);
  const doctor_id = doctorIdResult.rows[0]?.id;
  if (!doctor_id) {
    res.status(404).json({ success: false, message: 'Doctor not found.' });
    return;
  }
  const reportQuery = `
    SELECT t.*, d.name AS doctor_name FROM tracked_events t
    INNER JOIN Dr d ON t.actor_id = d.id::text
    WHERE actor_id = $1::text
    AND t.created_at::date = CURRENT_DATE
  `;
  const client = await db.connect();
  try {
    const result = await client.query(reportQuery, [doctor_id]);
    if (result.rows.length === 0) {
      res.status(404).json({ success: false, message: 'No reports found for this doctor.' });
      return;
    }

    const reportResponse = eventReportingService.reportEvent(result.rows);
    if (reportResponse === null) {
      res.status(500).json({ success: false, message: 'Failed to send report' });
      return;
    }
    res.status(200).json({ success: true, message: 'Report sent to Slack successfully.' });
  } catch (error) {
    logger.error('Error sending report to Slack:', error);
    res.status(500).json({ success: false, message: 'An error occurred while sending the report.' });
    return;
  } finally {
    client.release();
  }
});

export const buildMessengerReportAsImage: RequestHandler = catchAll(async (req, res) => {
  try {
    const current = DateTime.utc();
    const filterDate = req.body.text ? DateTime.fromFormat(req.body.text, 'yyyy-MM-dd') : current;
    const startDate = filterDate.toFormat('yyyy-MM-dd');
    const endDate = filterDate.plus({ days: 1 }).toFormat('yyyy-MM-dd');
    const doctorId = req.body.drId ?? undefined;

    const client = await db.connect();

    try {
      // Base doctor filter condition
      const doctorFilter = doctorId ? `AND "drId" = '${doctorId}'` : '';

      // Query for messenger treatment plans
      const messengerQuery = `
        SELECT
            COUNT(CASE WHEN tp.source = 'messenger' THEN 1 END) AS messenger_tps,
            COUNT(CASE WHEN outcome = 'Approve Unrestricted' AND tp.source = 'messenger' THEN 1 END) AS messenger_approved_unrestricted,
            COUNT(CASE WHEN outcome = 'Reject' AND tp.source = 'messenger' THEN 1 END) AS messenger_rejected
        FROM treatmentplan tp
        LEFT JOIN Patient p ON tp."patientID" = p."patientID"
        WHERE tp."createdAt" >= '${startDate}'
        AND tp."createdAt" < '${endDate}'
        AND (p.email IS NULL OR p.email != '${config.zelda.patientEmail}')
        AND (tp.type IS NULL OR tp.type != 'zelda-automated')
        ${doctorFilter.replace('"drId"', 'tp."drId"')};
      `;

      // Query to count rejected questionnaires (for pending review calculation)
      const rejectedQuestionnairesQuery = `
        SELECT
            (SELECT COUNT(*) FROM add_22_thc_questionnaire
             WHERE status = 'rejected'
             AND updated_at >= '${startDate}'
             AND updated_at < '${endDate}') +
            (SELECT COUNT(*) FROM extend_tp_questionnaire
             WHERE status = 'rejected'
             AND updated_at >= '${startDate}'
             AND updated_at < '${endDate}') +
            (SELECT COUNT(*) FROM quantity_increase_questionnaire
             WHERE status = 'rejected'
             AND updated_at >= '${startDate}'
             AND updated_at < '${endDate}') +
            (SELECT COUNT(*) FROM thc_increase_questionnaire
             WHERE status = 'rejected'
             AND updated_at >= '${startDate}'
             AND updated_at < '${endDate}') AS total_rejected_questionnaires;
      `;

      // Query for pending requests (same as system approved - submitted and eligible)
      const pendingRequestsQuery = `
        SELECT
            (SELECT COUNT(*) FROM add_22_thc_questionnaire
             WHERE status = 'submitted' AND is_eligible = true
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') +
            (SELECT COUNT(*) FROM extend_tp_questionnaire
             WHERE status = 'submitted' AND is_eligible = true
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') +
            (SELECT COUNT(*) FROM quantity_increase_questionnaire
             WHERE status = 'submitted' AND is_eligible = true
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') +
            (SELECT COUNT(*) FROM thc_increase_questionnaire
             WHERE status = 'submitted' AND is_eligible = true
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') AS total_pending_requests;
      `;

      // Query for system approvals and rejections (submitted status with eligibility check)
      const systemDecisionsQuery = `
        SELECT
            (SELECT COUNT(*) FROM add_22_thc_questionnaire
             WHERE status = 'submitted' AND is_eligible = true
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') +
            (SELECT COUNT(*) FROM extend_tp_questionnaire
             WHERE status = 'submitted' AND is_eligible = true
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') +
            (SELECT COUNT(*) FROM quantity_increase_questionnaire
             WHERE status = 'submitted' AND is_eligible = true
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') +
            (SELECT COUNT(*) FROM thc_increase_questionnaire
             WHERE status = 'submitted' AND is_eligible = true
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') AS total_system_approved,

            (SELECT COUNT(*) FROM add_22_thc_questionnaire
             WHERE status = 'submitted' AND is_eligible = false
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') +
            (SELECT COUNT(*) FROM extend_tp_questionnaire
             WHERE status = 'submitted' AND is_eligible = false
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') +
            (SELECT COUNT(*) FROM quantity_increase_questionnaire
             WHERE status = 'submitted' AND is_eligible = false
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') +
            (SELECT COUNT(*) FROM thc_increase_questionnaire
             WHERE status = 'submitted' AND is_eligible = false
             AND created_at >= '${startDate}'
             AND created_at < '${endDate}') AS total_system_rejected;
      `;

      // If doctorId is provided, get doctor name
      let doctorName = '';
      if (doctorId) {
        const doctorQuery = `SELECT username FROM Dr WHERE "accessID" = '${doctorId}' LIMIT 1;`;
        const doctorResult = await client.query(doctorQuery);
        if (doctorResult.rows.length > 0) {
          doctorName = doctorResult.rows[0].username;
        }
      }

      const messengerReport = await client.query(messengerQuery);
      const rejectedQuestionnaires = await client.query(rejectedQuestionnairesQuery);
      const pendingRequests = await client.query(pendingRequestsQuery);
      const systemDecisions = await client.query(systemDecisionsQuery);

      const stats = messengerReport.rows[0];
      const rejectedCount = rejectedQuestionnaires.rows[0].total_rejected_questionnaires;
      const pendingCount = pendingRequests.rows[0].total_pending_requests;
      const systemApprovedCount = systemDecisions.rows[0].total_system_approved;
      const systemRejectedCount = systemDecisions.rows[0].total_system_rejected;

      // Calculate totals
      const totalApproved = Number(stats.messenger_approved_unrestricted) || 0;
      const totalRejected = Number(rejectedCount) || 0;
      const totalPending = Number(pendingCount) || 0;
      const totalSystemApproved = Number(systemApprovedCount) || 0;
      const totalSystemRejected = Number(systemRejectedCount) || 0;

      const reportData = {
        reportDate: filterDate.toFormat('yyyy-MM-dd'),
        doctorName,
        pendingReview: totalPending,
        approved: totalApproved,
        rejected: totalRejected,
        systemApproved: totalSystemApproved,
        systemRejected: totalSystemRejected,
        total: totalApproved + totalRejected + totalPending + totalSystemApproved + totalSystemRejected,
      };

      // Generate HTML for the report
      const html = generateMessengerReportHtml(reportData);

      // Generate image from HTML
      const imageBuffer = await generateReportImageFromHtml(html);

      // Upload to S3
      const s3Service = new S3SalesReportsService();
      const reportDate = filterDate.toFormat('yyyy-MM-dd');

      const uploadResult = await s3Service.uploadSalesReport(
        imageBuffer,
        reportDate,
        REPORT_TYPES.MESSENGER,  // Use messenger report type
        'png',
        {
          reportTitle: `Doctor Messenger Report - ${reportDate}`,
          reportDescription: `Doctor messenger report generated at ${current.toFormat('HH:mm')} UTC`,
          reportData: {
            reportDate,
            reportType: 'messenger',
            doctorName,
            pendingReview: totalPending,
            approved: totalApproved,
            rejected: totalRejected,
            systemApproved: totalSystemApproved,
            systemRejected: totalSystemRejected,
            total: totalApproved + totalRejected + totalPending + totalSystemApproved + totalSystemRejected,
            generatedAt: current.toISO(),
          },
          slackChannel: 'C0904E5SW4W',
        }
      );

      if (!uploadResult.success) {
        throw new Error(`Failed to upload messenger report to S3: ${uploadResult.error}`);
      }

      logger.info(`Messenger report uploaded to S3 successfully`, {
        reportDate,
        s3Key: uploadResult.s3Key,
        fileUrl: uploadResult.fileUrl,
      });

      // Post Slack message with S3 image URL
      const slack = new WebClient(config.slackToken);
      const channel = 'C0904E5SW4W';

      const slackResponse = await slack.chat.postMessage({
        channel: channel,
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: '📱 Doctor Messenger Report',
              emoji: true,
            },
          },
          {
            type: 'image',
            image_url: uploadResult.fileUrl!,
            alt_text: 'Doctor Messenger Report',
          },
          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: `:clock3: Report generated on ${new Date().toLocaleString()}`,
              },
            ],
          }
        ],
        text: 'Doctor Messenger Report',
      });

      // Update database with Slack message info
      if (slackResponse.ok && slackResponse.ts) {
        await s3Service.updateSlackInfo(
          reportDate,
          REPORT_TYPES.MESSENGER,
          slackResponse.ts,
          channel
        );
      }

      logger.info(`Messenger report image for ${reportDate} sent successfully.`, {
        s3Key: uploadResult.s3Key,
        fileUrl: uploadResult.fileUrl,
        slackMessageTs: slackResponse.ts,
      });

      res.status(200).json({
        success: true,
        s3Key: uploadResult.s3Key,
        fileUrl: uploadResult.fileUrl,
        reportData,
      });
      return;
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error('Error building messenger report image:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while building the messenger report image',
    });
    return;
  }
});

export const buildSalesAgentReportAsImage: RequestHandler = catchAll(async (_req, res) => {
  try {
    const token = await getAuthToken();
    const current = DateTime.utc();
    const nextDay = current.plus({ days: 1 });

    const from = current.toFormat('yyyy-MM-dd');
    const to = nextDay.toFormat('yyyy-MM-dd');

    const queryParams = `https://vas.softphone.pro/api/history/calls/export?dateFrom=${from}T00:00:00%2B05:00&dateTo=${to}T00:00:00%2B05:00&localTimeZoneOffset=%2B300&typeFile=csv`;

    const getReportSoftphone = await axios.get(queryParams, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const csvData = getReportSoftphone.data;
    const data = csvToJson(csvData, ';');

    const headers = await ZohoAuth.getHeaders();

    const query = QUERY_CONVERSION();
    const response = await axios.post(zohoQueryURL, query, {
      headers,
    });
    const zohoData = response.data.data as ZohoConversion[];

    const report = generateAgentReport(data, zohoData, AGENTSLIST);

    // Generate HTML for the report
    const html = generateSalesAgentReportHtml(report);

    // Generate image from HTML
    const imageBuffer = await generateReportImageFromHtml(html);

    // Upload to S3 instead of saving locally
    const s3Service = new S3SalesReportsService();
    const reportDate = current.toFormat('yyyy-MM-dd');

    const uploadResult = await s3Service.uploadSalesReport(
      imageBuffer,
      reportDate,
      REPORT_TYPES.SALES_AGENT,  // Use constant for consistency and avoid conflicts
      'png',
      {
        reportTitle: `Sales Agent Daily Report - ${reportDate}`,
        reportDescription: `Sales agent performance report generated at ${current.toFormat('HH:mm')} UTC`,
        reportData: {
          reportDate,
          reportType: 'sales-agent',
          agentCount: report.length,
          totalCalls: report.reduce((sum, agent) => sum + (typeof agent.calls === 'number' ? agent.calls : parseInt(agent.calls) || 0), 0),
          generatedAt: current.toISO(),
        },
        slackChannel: 'C08VC12V80N',
      }
    );

    if (!uploadResult.success) {
      throw new Error(`Failed to upload sales agent report to S3: ${uploadResult.error}`);
    }

    logger.info(`Sales agent report uploaded to S3 successfully`, {
      reportDate,
      s3Key: uploadResult.s3Key,
      fileUrl: uploadResult.fileUrl,
    });

    // Post Slack message with S3 image URL
    const slack = new WebClient(config.slackToken);
    const channel = 'C08VC12V80N'; // Same channel as in the original function

    const slackResponse = await slack.chat.postMessage({
      channel: channel,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '📊 Softphone Report',
            emoji: true,
          },
        },
        {
          type: 'image',
          image_url: uploadResult.fileUrl!,
          alt_text: 'Sales Agent Report',
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `:clock3: Report generated on ${new Date().toLocaleString()}`,
            },
          ],
        }
      ],
      text: 'Sales Team Daily Report',
    });

    // Update database with Slack message info
    if (slackResponse.ok && slackResponse.ts) {
      await s3Service.updateSlackInfo(
        reportDate,
        REPORT_TYPES.SALES_AGENT,  // Use constant for consistency
        slackResponse.ts,
        channel
      );
    }

    logger.info(`Sales agent report image for ${reportDate} sent successfully.`, {
      s3Key: uploadResult.s3Key,
      fileUrl: uploadResult.fileUrl,
      slackMessageTs: slackResponse.ts,
    });

    res.status(200).json({
      success: true,
      s3Key: uploadResult.s3Key,
      fileUrl: uploadResult.fileUrl,
    });
    return;
  } catch (error) {
    logger.error('Error building sales agent report image:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while building the sales agent report image',
    });
    return;
  }
});

/**
 * Generate a PNG image from HTML using Puppeteer
 */
async function generateReportImageFromHtml(html: string): Promise<Buffer> {
  const browser = await puppeteer.launch({ args: ['--no-sandbox', '--disable-setuid-sandbox'] });
  const page = await browser.newPage();
  await page.setContent(html, { waitUntil: 'networkidle0' });
  const element = await page.$('.report-container');
  if (!element) throw new Error('Could not find .report-container in HTML');
  const imageBuffer = await element.screenshot({ type: 'png' });
  await browser.close();
  return imageBuffer as Buffer;
}

/**
 * Generate HTML for the sales agent report
 */
function generateSalesAgentReportHtml(report: AgentReport[]): string {
  // Calculate totals for the entire report
  const totals = report.reduce((acc, agent) => {
    // Helper function to safely parse numbers or return 0 for non-numeric values
    const safeParseInt = (value: number | string | undefined): number => {
      if (value === 'N/A' || value === null || value === undefined) return 0;
      const parsed = typeof value === 'number' ? value : parseInt(value);
      return isNaN(parsed) ? 0 : parsed;
    };

    return {
      calls: acc.calls + safeParseInt(agent.calls),
      in: acc.in + safeParseInt(agent.in),
      ans: acc.ans + safeParseInt(agent.ans),
      out: acc.out + safeParseInt(agent.out),
      suc: acc.suc + safeParseInt(agent.suc),
      bookings: acc.bookings + safeParseInt(agent.bookings as number | string),
      // Convert time strings to seconds, add them, then convert back to string format
      talkTime: sumTimeStrings(acc.talkTime, agent.talkTime as string),
      waitTime: sumTimeStrings(acc.waitTime, agent.waitTime as string)
    };
  }, { calls: 0, in: 0, ans: 0, out: 0, suc: 0, bookings: 0, talkTime: '00:00:00', waitTime: '00:00:00' });

  // Calculate percentages for the totals
  const totalInboundAnsweredPercent = totals.in > 0 ? Math.round((totals.ans / totals.in) * 100) : 0;
  const totalOutboundSuccessfulPercent = totals.out > 0 ? Math.round((totals.suc / totals.out) * 100) : 0;

  // Format the table content
  const headerRow = `
    <tr class="header-row">
      <th class="metric-column">Metric</th>
      ${report.map(agent => `<th class="agent-column">${agent.agent}</th>`).join('')}
      <th class="total-column">Total</th>
    </tr>
  `;

  const metricsData = [
    { label: 'Total Calls', key: 'calls' },
    { label: 'Inbound', key: 'in' },
    { label: 'Inbound Answered', key: 'ans', percentKey: 'percentAns', totalPercent: `${totalInboundAnsweredPercent}%` },
    { label: 'Outbound', key: 'out' },
    { label: 'Outbound Successful', key: 'suc', percentKey: 'percentSuc', totalPercent: `${totalOutboundSuccessfulPercent}%` },
    { label: 'Bookings', key: 'bookings' },
    { label: 'Total Talk Time', key: 'talkTime' },
    { label: 'Total Wait Time', key: 'waitTime' }
  ];

  const dataRows = metricsData.map(metric => {
    const cells = report.map(agent => {
      let cellValue = agent[metric.key];
      if (metric.percentKey && agent[metric.percentKey]) {
        cellValue = `${cellValue} (${agent[metric.percentKey]})`;
      }
      return `<td>${cellValue}</td>`;
    });

    // Get total value for this metric
    let totalValue: string;
    if (metric.key === 'talkTime' || metric.key === 'waitTime') {
      totalValue = totals[metric.key as keyof typeof totals] as string;
    } else if (metric.key === 'bookings') {
      // Special handling for bookings to show the sum only if it's not zero
      const bookingsTotal = totals.bookings;
      totalValue = bookingsTotal > 0 ? String(bookingsTotal) : '';
    } else {
      totalValue = String(totals[metric.key as keyof typeof totals] || '');
      if (metric.totalPercent) {
        totalValue = `${totalValue} (${metric.totalPercent})`;
      }
    }
    
    return `
      <tr>
        <td class="metric-name">${metric.label}</td>
        ${cells.join('')}
        <td class="total-value">${totalValue}</td>
      </tr>
    `;
  }).join('');

  // Use DateTime from luxon (which is already imported) to get Australia/Sydney timezone
  const sydneyTime = DateTime.now().setZone('Australia/Sydney');
  // Format date as "d MMMM yyyy" (day month-name year) as per Australian standard
  // Example: "21 July 2025"
  const formattedDate = sydneyTime.toFormat('d MMMM yyyy');
  const formattedTime = sydneyTime.toFormat('h:mm a');  // Removed seconds for cleaner look
  const formattedDateTime = `${formattedDate}, ${formattedTime}`;

  return `
    <html>
      <head>
        <meta charset="utf-8" />
        <style>
          body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #f5f5f7;
            color: #333;
            margin: 0;
            padding: 20px;
          }
          .report-container {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            overflow: hidden;
            width: fit-content;
            margin: 0 auto;
          }
          .report-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e1e1e1;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .report-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
          }
          .report-title:before {
            content: '📞';
          }
          .report-meta {
            color: #888;
            font-size: 13px;
          }
          #report-table {
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
            table-layout: auto; /* Changed from fixed to auto */
          }
          #report-table th, #report-table td {
            padding: 10px 16px;
            text-align: left;
            border-bottom: 1px solid #e1e1e1;
            white-space: nowrap; /* Keep this to prevent line breaks */
          }
          .header-row {
            background-color: #f5f5f7;
          }
          .header-row th {
            font-weight: 600;
          }
          .metric-column {
            min-width: 150px;
            font-weight: 600;
          }
          .agent-column {
            min-width: 100px;
          }
          .total-column {
            font-weight: 600;
            background-color: #f0f0f2;
            min-width: 100px;
          }
          .metric-name {
            font-weight: 500;
            min-width: 180px; /* Ensure enough space for the full metric name */
          }
          .total-value {
            font-weight: 600;
            background-color: #f0f0f2;
            white-space: nowrap;
          }
          tr:hover {
            background-color: #f9f9fb;
          }
        </style>
      </head>
      <body>
        <div class="report-container">
          <div class="report-header">
            <div class="report-title">Softphone Daily Report</div>
            <div class="report-meta">Generated: ${formattedDateTime}</div>
          </div>
          <table id="report-table">
            ${headerRow}
            ${dataRows}
          </table>
        </div>
      </body>
    </html>
  `;
}

/**
 * Generate HTML for the messenger report
 */
function generateMessengerReportHtml(reportData: {
  reportDate: string;
  doctorName: string;
  pendingReview: number;
  approved: number;
  rejected: number;
  systemApproved: number;
  systemRejected: number;
  total: number;
}): string {
  // Use DateTime from luxon to get Australia/Sydney timezone
  const sydneyTime = DateTime.now().setZone('Australia/Sydney');
  const formattedDate = sydneyTime.toFormat('d MMMM yyyy');
  const formattedTime = sydneyTime.toFormat('h:mm a');
  const formattedDateTime = `${formattedDate}, ${formattedTime}`;

  return `
    <html>
      <head>
        <meta charset="utf-8" />
        <style>
          body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #f5f5f7;
            color: #333;
            margin: 0;
            padding: 20px;
          }
          .report-container {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            overflow: hidden;
            width: fit-content;
            margin: 0 auto;
            min-width: 400px;
          }
          .report-header {
            padding: 20px 20px 16px 20px;
            border-bottom: 1px solid #e1e1e1;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
          }
          .report-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
          }
          .report-title:before {
            content: '●';
            font-size: 16px;
            color: #007aff;
            margin-right: 8px;
            display: inline-block;
            width: 20px;
            height: 20px;
            background: #007aff;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            font-size: 12px;
            color: white;
          }
          .report-meta {
            color: #666;
            font-size: 12px;
            font-weight: 400;
            margin-left: 32px;
          }
          .report-content {
            padding: 20px;
          }
          .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
          }
          .metric-row:last-child {
            border-bottom: none;
            font-weight: 600;
            background-color: #f9f9fb;
            margin: 10px -20px -20px -20px;
            padding: 15px 20px;
          }
          .metric-label {
            font-weight: 500;
            color: #555;
          }
          .metric-value {
            font-weight: 600;
            color: #333;
            font-size: 16px;
          }
          .pending { color: #ff9500; }
          .approved { color: #34c759; }
          .rejected { color: #ff3b30; }
          .system-approved { color: #30d158; }
          .system-rejected { color: #ff453a; }
          .total { color: #007aff; }
          .doctor-info {
            background-color: #f0f0f2;
            padding: 10px 15px;
            margin: 15px -20px 15px -20px;
            font-size: 13px;
            color: #666;
            text-align: center;
          }
        </style>
      </head>
      <body>
        <div class="report-container">
          <div class="report-header">
            <div class="report-title">Doctor Messenger Report</div>
            <div class="report-meta">Generated: ${formattedDateTime}</div>
          </div>
          <div class="report-content">
            <div class="metric-row">
              <span class="metric-label">Report Date:</span>
              <span class="metric-value">${reportData.reportDate}</span>
            </div>
            ${reportData.doctorName ? `
            <div class="doctor-info">
              Doctor: ${reportData.doctorName}
            </div>
            ` : ''}
            <div class="metric-row">
              <span class="metric-label">Pending Review:</span>
              <span class="metric-value pending">${reportData.pendingReview}</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">Approved (last 24 hours):</span>
              <span class="metric-value approved">${reportData.approved}</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">Rejected (last 24 hours):</span>
              <span class="metric-value rejected">${reportData.rejected}</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">System Approved:</span>
              <span class="metric-value system-approved">${reportData.systemApproved}</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">System Rejected:</span>
              <span class="metric-value system-rejected">${reportData.systemRejected}</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">Total Requests:</span>
              <span class="metric-value total">${reportData.total}</span>
            </div>
          </div>
        </div>
      </body>
    </html>
  `;
}

/**
 * Helper function to sum time strings in format "HH:MM:SS"
 */
function sumTimeStrings(time1: string, time2: string): string {
  // Convert time strings to seconds
  const getSeconds = (timeStr: string) => {
    if (!timeStr || timeStr === '00:00:00') return 0;
    const [hours, minutes, seconds] = timeStr.split(':').map(num => parseInt(num, 10));
    return hours * 3600 + minutes * 60 + seconds;
  };

  const totalSeconds = getSeconds(time1) + getSeconds(time2);
  
  // Convert back to HH:MM:SS
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}
