import {
  buildSalesAgentReport,
  buildSalesAgentReportAsImage,
  churnReport,
  getLeads,
  medecineRegister,
  verifyPin,
  trackEvent,
  sendDetailedReportToSlack,
} from '../controllers/reports';
import { validateDr } from '../middlewares/validationMiddleware';
import express from 'express';

const router = express.Router();

const currentVersion = 'v1.0';

router.post(`/${currentVersion}/report/retreive`, churnReport);
router.post(`/${currentVersion}/retreive-patients`, getLeads);
router.post(`/${currentVersion}/sales-agent`, buildSalesAgentReport);
router.post(`/${currentVersion}/sales-agent-image`, buildSalesAgentReportAsImage);
router.post(`/${currentVersion}/medicine-register`, medecineRegister);
router.post(`/${currentVersion}/verify-pin`, verifyPin);
router.post(`/${currentVersion}/track-event`, validateDr, trackEvent);
router.post(`/${currentVersion}/send-tracking-report`, validateDr, sendDetailedReportToSlack);

export default router;
