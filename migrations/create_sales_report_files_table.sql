-- Migration: Create sales_report_files table for tracking S3-stored sales report files
-- Description: This table tracks sales report files uploaded to S3, replacing local file storage
-- Date: 2025-01-24

-- Create the sales_report_files table
CREATE TABLE IF NOT EXISTS sales_report_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Report identification
  report_date DATE NOT NULL,
  report_type VARCHAR(50) NOT NULL CHECK (report_type IN ('daily', 'weekly', 'monthly', 'custom', 'sales-agent')),
  report_format VARCHAR(20) NOT NULL DEFAULT 'png' CHECK (report_format IN ('png', 'pdf', 'html')),
  
  -- S3 file information
  s3_bucket VARCHAR(255) NOT NULL,
  s3_key TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_size_bytes INTEGER,
  content_type VARCHAR(100) DEFAULT 'image/png',
  
  -- Report metadata
  report_title TEXT,
  report_description TEXT,
  report_data JSONB, -- Store report summary data for quick access
  
  -- Slack integration
  slack_channel VARCHAR(100),
  slack_message_ts TEXT,
  slack_sent_at TIMESTAMPTZ,
  
  -- Status tracking
  status VARCHAR(50) NOT NULL DEFAULT 'generated' CHECK (status IN ('generated', 'uploaded', 'sent', 'failed', 'archived')),
  error_message TEXT,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  
  -- Constraints
  UNIQUE(report_date, report_type, report_format)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_sales_report_files_report_date ON sales_report_files(report_date);
CREATE INDEX IF NOT EXISTS idx_sales_report_files_report_type ON sales_report_files(report_type);
CREATE INDEX IF NOT EXISTS idx_sales_report_files_status ON sales_report_files(status);
CREATE INDEX IF NOT EXISTS idx_sales_report_files_created_at ON sales_report_files(created_at);
CREATE INDEX IF NOT EXISTS idx_sales_report_files_s3_key ON sales_report_files(s3_key);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_sales_report_files_date_type_status 
ON sales_report_files(report_date, report_type, status);

-- Add comments for documentation
COMMENT ON TABLE sales_report_files IS 'Tracks sales report files stored in Amazon S3';
COMMENT ON COLUMN sales_report_files.report_date IS 'Date the report covers (not creation date)';
COMMENT ON COLUMN sales_report_files.report_type IS 'Type of report: daily, weekly, monthly, or custom';
COMMENT ON COLUMN sales_report_files.s3_key IS 'S3 object key/path for the file';
COMMENT ON COLUMN sales_report_files.file_url IS 'Full S3 URL for accessing the file';
COMMENT ON COLUMN sales_report_files.report_data IS 'JSON summary of report data for quick access without downloading file';
COMMENT ON COLUMN sales_report_files.slack_message_ts IS 'Slack message timestamp for tracking sent messages';
COMMENT ON COLUMN sales_report_files.status IS 'Current status of the report file lifecycle';

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_sales_report_files_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_sales_report_files_updated_at
    BEFORE UPDATE ON sales_report_files
    FOR EACH ROW
    EXECUTE FUNCTION update_sales_report_files_updated_at();

-- Insert initial test data (optional - can be removed in production)
-- INSERT INTO sales_report_files (
--   report_date, 
--   report_type, 
--   s3_bucket, 
--   s3_key, 
--   file_url, 
--   file_name,
--   report_title,
--   status
-- ) VALUES (
--   CURRENT_DATE - INTERVAL '1 day',
--   'daily',
--   'zenith-sales-reports',
--   'sales-reports/daily/report-2025-01-23.png',
--   'https://zenith-sales-reports.s3.ap-southeast-2.amazonaws.com/sales-reports/daily/report-2025-01-23.png',
--   'report-2025-01-23.png',
--   'Daily Sales Report - 2025-01-23',
--   'generated'
-- );
